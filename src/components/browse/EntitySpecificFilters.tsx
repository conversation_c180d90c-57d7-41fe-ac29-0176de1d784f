'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  GraduationCap,
  Briefcase,
  Cpu,
  Calendar,
  X,
  Wrench,
  Building2,
  Code,
  FileText,
  Headphones,
  Users,
  DollarSign,
  Mail,
  BookOpen,
  Info
} from 'lucide-react';


interface EntitySpecificFiltersProps {
  selectedEntityTypes: string[];
  onFilterChange: (filterName: string, value: any) => void;
  allEntityTypes: Array<{ id: string; name: string }>;
  currentFilters?: Record<string, any>; // Add current filter values for controlled inputs
}

// Entity type specific filter definitions
const COURSE_FILTERS = {
  skill_levels: {
    label: 'Skill Levels',
    type: 'multiselect',
    options: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
  },
  certificate_available: {
    label: 'Certificate Available',
    type: 'boolean'
  },
  instructor_name: {
    label: 'Instructor Name',
    type: 'text',
    placeholder: 'Search by instructor...'
  },
  duration_text: {
    label: 'Duration',
    type: 'text',
    placeholder: 'e.g., 10 hours, 6 weeks...'
  },
  enrollment_min: {
    label: 'Min Enrollment',
    type: 'number',
    placeholder: '0'
  },
  enrollment_max: {
    label: 'Max Enrollment',
    type: 'number',
    placeholder: '10000'
  },
  prerequisites: {
    label: 'Prerequisites',
    type: 'text',
    placeholder: 'Required background...'
  },
  has_syllabus: {
    label: 'Has Syllabus',
    type: 'boolean'
  }
};

const JOB_FILTERS = {
  employment_types: {
    label: 'Employment Types',
    type: 'multiselect',
    options: ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship']
  },
  experience_levels: {
    label: 'Experience Levels',
    type: 'multiselect',
    options: ['Entry', 'Mid', 'Senior', 'Lead', 'Executive']
  },
  location_types: {
    label: 'Location Types',
    type: 'multiselect',
    options: ['Remote', 'On-site', 'Hybrid']
  },
  company_name: {
    label: 'Company Name',
    type: 'text',
    placeholder: 'Search by company...'
  },
  job_title: {
    label: 'Job Title',
    type: 'text',
    placeholder: 'Search by role...'
  },
  salary_min: {
    label: 'Min Salary (K)',
    type: 'number',
    placeholder: '50'
  },
  salary_max: {
    label: 'Max Salary (K)',
    type: 'number',
    placeholder: '200'
  },
  job_description: {
    label: 'Job Description',
    type: 'text',
    placeholder: 'Search job description...'
  },
  has_application_url: {
    label: 'Direct Application Available',
    type: 'boolean'
  }
};

const HARDWARE_FILTERS = {
  hardware_types: {
    label: 'Hardware Types',
    type: 'multiselect',
    options: ['GPU', 'CPU', 'FPGA', 'TPU', 'ASIC', 'Memory', 'Storage']
  },
  manufacturers: {
    label: 'Manufacturers',
    type: 'multiselect',
    options: ['NVIDIA', 'Intel', 'AMD', 'Apple', 'Google', 'Qualcomm', 'Samsung']
  },
  release_date_from: {
    label: 'Release Date From',
    type: 'date'
  },
  release_date_to: {
    label: 'Release Date To',
    type: 'date'
  },
  price_min: {
    label: 'Min Price ($)',
    type: 'number',
    placeholder: '100'
  },
  price_max: {
    label: 'Max Price ($)',
    type: 'number',
    placeholder: '5000'
  },
  specifications_search: {
    label: 'Specifications',
    type: 'text',
    placeholder: 'e.g., GDDR6, PCIe 4.0...'
  },
  has_datasheet: {
    label: 'Has Datasheet',
    type: 'boolean'
  },
  memory_search: {
    label: 'Memory',
    type: 'text',
    placeholder: 'e.g., 16GB, 32GB...'
  },
  processor_search: {
    label: 'Processor',
    type: 'text',
    placeholder: 'e.g., Intel i9, M2...'
  }
};

const EVENT_FILTERS = {
  event_types: {
    label: 'Event Types',
    type: 'multiselect',
    options: ['Conference', 'Workshop', 'Webinar', 'Meetup', 'Hackathon', 'Summit', 'Bootcamp']
  },
  start_date_from: {
    label: 'Start Date From',
    type: 'date'
  },
  start_date_to: {
    label: 'Start Date To',
    type: 'date'
  },
  end_date_from: {
    label: 'End Date From',
    type: 'date'
  },
  end_date_to: {
    label: 'End Date To',
    type: 'date'
  },
  is_online: {
    label: 'Online Event',
    type: 'boolean'
  },
  location: {
    label: 'Location',
    type: 'text',
    placeholder: 'City, Country...'
  },
  price_text: {
    label: 'Price',
    type: 'text',
    placeholder: 'Free, $100, etc...'
  },
  registration_required: {
    label: 'Registration Required',
    type: 'boolean'
  },
  has_registration_url: {
    label: 'Registration Link Available',
    type: 'boolean'
  },
  speakers_search: {
    label: 'Speakers',
    type: 'text',
    placeholder: 'Search by speaker name...'
  },
  target_audience_search: {
    label: 'Target Audience',
    type: 'text',
    placeholder: 'developers, researchers...'
  }
};

const TOOL_FILTERS = {
  technical_levels: {
    label: 'Technical Levels',
    type: 'multiselect',
    options: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
  },
  learning_curves: {
    label: 'Learning Curves',
    type: 'multiselect',
    options: ['LOW', 'MEDIUM', 'HIGH']
  },
  pricing_models: {
    label: 'Pricing Models',
    type: 'multiselect',
    options: ['FREE', 'FREEMIUM', 'PAID', 'CONTACT_SALES']
  },
  price_ranges: {
    label: 'Price Ranges',
    type: 'multiselect',
    options: ['FREE', 'LOW', 'MEDIUM', 'HIGH', 'ENTERPRISE']
  },
  has_api: {
    label: 'Has API',
    type: 'boolean'
  },
  has_free_tier: {
    label: 'Has Free Tier',
    type: 'boolean'
  },
  open_source: {
    label: 'Open Source',
    type: 'boolean'
  },
  mobile_support: {
    label: 'Mobile Support',
    type: 'boolean'
  },
  demo_available: {
    label: 'Demo Available',
    type: 'boolean'
  },
  platforms: {
    label: 'Platforms',
    type: 'multiselect',
    options: ['Web', 'iOS', 'Android', 'Desktop', 'API']
  },
  integrations: {
    label: 'Integrations',
    type: 'multiselect',
    options: ['Slack', 'Discord', 'Zapier', 'API', 'Webhook']
  },
  frameworks: {
    label: 'Frameworks',
    type: 'multiselect',
    options: ['TensorFlow', 'PyTorch', 'Hugging Face', 'OpenAI', 'Custom']
  },
  libraries: {
    label: 'Libraries',
    type: 'multiselect',
    options: ['OpenAI', 'Anthropic', 'Cohere', 'Google', 'Custom']
  },
  key_features_search: {
    label: 'Key Features',
    type: 'text',
    placeholder: 'e.g., natural language processing...'
  },
  use_cases_search: {
    label: 'Use Cases',
    type: 'text',
    placeholder: 'e.g., content generation...'
  },
  target_audience_search: {
    label: 'Target Audience',
    type: 'text',
    placeholder: 'e.g., developers, marketers...'
  },
  deployment_options: {
    label: 'Deployment Options',
    type: 'multiselect',
    options: ['Cloud', 'On-premise', 'Hybrid', 'SaaS']
  },
  support_channels: {
    label: 'Support Channels',
    type: 'multiselect',
    options: ['Email', 'Chat', 'Phone', 'Community', 'Documentation']
  },
  has_live_chat: {
    label: 'Has Live Chat',
    type: 'boolean'
  },
  customization_level: {
    label: 'Customization Level',
    type: 'text',
    placeholder: 'e.g., high, medium, low...'
  },
  pricing_details_search: {
    label: 'Pricing Details',
    type: 'text',
    placeholder: 'e.g., per user, per month...'
  }
};

const AGENCY_FILTERS = {
  services_offered: {
    label: 'Services Offered',
    type: 'multiselect',
    options: ['AI Strategy', 'Machine Learning', 'Data Science', 'Automation', 'Consulting', 'Development']
  },
  industry_focus: {
    label: 'Industry Focus',
    type: 'multiselect',
    options: ['Healthcare', 'Finance', 'E-commerce', 'Manufacturing', 'Education', 'Government']
  },
  target_client_size: {
    label: 'Target Client Size',
    type: 'multiselect',
    options: ['Startup', 'SMB', 'Enterprise', 'Fortune 500']
  },
  target_audience: {
    label: 'Target Audience',
    type: 'multiselect',
    options: ['CTOs', 'Data Scientists', 'Business Leaders', 'Developers', 'Product Managers']
  },
  location_summary: {
    label: 'Location',
    type: 'text',
    placeholder: 'e.g., San Francisco, Remote...'
  },
  has_portfolio: {
    label: 'Has Portfolio',
    type: 'boolean'
  },
  pricing_info_search: {
    label: 'Pricing Info',
    type: 'text',
    placeholder: 'e.g., hourly rate, project-based...'
  },
  services_search: {
    label: 'Services Search',
    type: 'text',
    placeholder: 'e.g., machine learning, AI strategy...'
  },
  industry_search: {
    label: 'Industry Search',
    type: 'text',
    placeholder: 'e.g., healthcare, finance...'
  },
  audience_search: {
    label: 'Audience Search',
    type: 'text',
    placeholder: 'e.g., developers, executives...'
  }
};

const SOFTWARE_FILTERS = {
  license_types: {
    label: 'License Types',
    type: 'multiselect',
    options: ['MIT', 'Apache 2.0', 'GPL', 'Commercial', 'Proprietary', 'BSD']
  },
  programming_languages: {
    label: 'Programming Languages',
    type: 'multiselect',
    options: ['Python', 'JavaScript', 'Java', 'C++', 'R', 'Go', 'Rust']
  },
  platform_compatibility: {
    label: 'Platform Compatibility',
    type: 'multiselect',
    options: ['Windows', 'macOS', 'Linux', 'Web', 'Mobile', 'Docker']
  },
  open_source: {
    label: 'Open Source',
    type: 'boolean'
  },
  has_repository: {
    label: 'Has Repository',
    type: 'boolean'
  },
  current_version: {
    label: 'Current Version',
    type: 'text',
    placeholder: 'e.g., 2.0, v1.5.3...'
  },
  release_date_from: {
    label: 'Release Date From',
    type: 'date'
  },
  release_date_to: {
    label: 'Release Date To',
    type: 'date'
  },
  languages_search: {
    label: 'Languages Search',
    type: 'text',
    placeholder: 'e.g., python, javascript...'
  },
  platforms_search: {
    label: 'Platforms Search',
    type: 'text',
    placeholder: 'e.g., linux, windows...'
  },
  license_search: {
    label: 'License Search',
    type: 'text',
    placeholder: 'e.g., MIT, Apache...'
  }
};

const RESEARCH_PAPER_FILTERS = {
  authors: {
    label: 'Authors',
    type: 'multiselect',
    options: ['Geoffrey Hinton', 'Yann LeCun', 'Yoshua Bengio', 'Andrew Ng', 'Fei-Fei Li']
  },
  research_areas: {
    label: 'Research Areas',
    type: 'multiselect',
    options: ['Machine Learning', 'Natural Language Processing', 'Computer Vision', 'Robotics', 'AI Safety']
  },
  publication_venues: {
    label: 'Publication Venues',
    type: 'multiselect',
    options: ['NeurIPS', 'ICML', 'ICLR', 'AAAI', 'Nature', 'Science', 'JMLR']
  },
  keywords: {
    label: 'Keywords',
    type: 'multiselect',
    options: ['transformer', 'attention', 'neural network', 'deep learning', 'reinforcement learning']
  },
  doi: {
    label: 'DOI',
    type: 'text',
    placeholder: 'e.g., 10.1038/...'
  },
  publication_date_from: {
    label: 'Publication Date From',
    type: 'date'
  },
  publication_date_to: {
    label: 'Publication Date To',
    type: 'date'
  },
  citation_count_min: {
    label: 'Min Citations',
    type: 'number',
    placeholder: '100'
  },
  citation_count_max: {
    label: 'Max Citations',
    type: 'number',
    placeholder: '10000'
  },
  abstract_search: {
    label: 'Abstract Search',
    type: 'text',
    placeholder: 'e.g., attention mechanism...'
  },
  authors_search: {
    label: 'Authors Search',
    type: 'text',
    placeholder: 'e.g., hinton, lecun...'
  },
  research_areas_search: {
    label: 'Research Areas Search',
    type: 'text',
    placeholder: 'e.g., nlp, computer vision...'
  },
  venues_search: {
    label: 'Venues Search',
    type: 'text',
    placeholder: 'e.g., neurips, icml...'
  },
  keywords_search: {
    label: 'Keywords Search',
    type: 'text',
    placeholder: 'e.g., transformer, attention...'
  }
};

const PODCAST_FILTERS = {
  host: {
    label: 'Host',
    type: 'text',
    placeholder: 'e.g., Lex Fridman, Andrew Ng...'
  },
  main_topics: {
    label: 'Main Topics',
    type: 'multiselect',
    options: ['AI', 'Machine Learning', 'Deep Learning', 'Robotics', 'Data Science', 'Tech News']
  },
  frequency: {
    label: 'Frequency',
    type: 'multiselect',
    options: ['Weekly', 'Bi-weekly', 'Monthly', 'Daily', 'Irregular']
  },
  average_length: {
    label: 'Average Length',
    type: 'text',
    placeholder: 'e.g., 60 minutes, 2 hours...'
  },
  has_spotify: {
    label: 'Available on Spotify',
    type: 'boolean'
  },
  has_apple_podcasts: {
    label: 'Available on Apple Podcasts',
    type: 'boolean'
  },
  has_google_podcasts: {
    label: 'Available on Google Podcasts',
    type: 'boolean'
  },
  has_youtube: {
    label: 'Available on YouTube',
    type: 'boolean'
  },
  topics_search: {
    label: 'Topics Search',
    type: 'text',
    placeholder: 'e.g., machine learning, AI...'
  },
  frequency_search: {
    label: 'Frequency Search',
    type: 'text',
    placeholder: 'e.g., weekly, monthly...'
  }
};

const COMMUNITY_FILTERS = {
  community_types: {
    label: 'Community Types',
    type: 'multiselect',
    options: ['Discord', 'Slack', 'Reddit', 'Forum', 'Telegram', 'Facebook Group']
  },
  focus_areas: {
    label: 'Focus Areas',
    type: 'multiselect',
    options: ['Machine Learning', 'AI Research', 'Startups', 'Open Source', 'Career Development']
  },
  target_audience: {
    label: 'Target Audience',
    type: 'multiselect',
    options: ['Developers', 'Researchers', 'Students', 'Entrepreneurs', 'Data Scientists']
  },
  member_count_min: {
    label: 'Min Members',
    type: 'number',
    placeholder: '1000'
  },
  member_count_max: {
    label: 'Max Members',
    type: 'number',
    placeholder: '100000'
  },
  is_free: {
    label: 'Free to Join',
    type: 'boolean'
  },
  requires_invitation: {
    label: 'Requires Invitation',
    type: 'boolean'
  },
  has_events: {
    label: 'Has Events',
    type: 'boolean'
  },
  focus_areas_search: {
    label: 'Focus Areas Search',
    type: 'text',
    placeholder: 'e.g., machine learning, AI...'
  },
  audience_search: {
    label: 'Audience Search',
    type: 'text',
    placeholder: 'e.g., developers, researchers...'
  },
  type_search: {
    label: 'Type Search',
    type: 'text',
    placeholder: 'e.g., discord, slack...'
  }
};

const GRANT_FILTERS = {
  funding_organizations: {
    label: 'Funding Organizations',
    type: 'multiselect',
    options: ['NSF', 'NIH', 'DARPA', 'EU Horizon', 'Google Research', 'Microsoft Research']
  },
  research_areas: {
    label: 'Research Areas',
    type: 'multiselect',
    options: ['AI Safety', 'Machine Learning', 'Robotics', 'NLP', 'Computer Vision', 'Ethics']
  },
  eligible_applicants: {
    label: 'Eligible Applicants',
    type: 'multiselect',
    options: ['Universities', 'Startups', 'Non-profits', 'Individuals', 'Government', 'Industry']
  },
  funding_amount_min: {
    label: 'Min Funding (K)',
    type: 'number',
    placeholder: '50'
  },
  funding_amount_max: {
    label: 'Max Funding (K)',
    type: 'number',
    placeholder: '1000'
  },
  deadline_from: {
    label: 'Deadline From',
    type: 'date'
  },
  deadline_to: {
    label: 'Deadline To',
    type: 'date'
  },
  is_open: {
    label: 'Currently Open',
    type: 'boolean'
  },
  supports_international: {
    label: 'Supports International',
    type: 'boolean'
  },
  research_areas_search: {
    label: 'Research Areas Search',
    type: 'text',
    placeholder: 'e.g., machine learning, AI safety...'
  },
  organization_search: {
    label: 'Organization Search',
    type: 'text',
    placeholder: 'e.g., nsf, darpa...'
  },
  applicants_search: {
    label: 'Applicants Search',
    type: 'text',
    placeholder: 'e.g., startup, university...'
  }
};

const NEWSLETTER_FILTERS = {
  frequency: {
    label: 'Frequency',
    type: 'multiselect',
    options: ['Daily', 'Weekly', 'Bi-weekly', 'Monthly', 'Irregular']
  },
  focus_areas: {
    label: 'Focus Areas',
    type: 'multiselect',
    options: ['AI News', 'Research', 'Industry Updates', 'Tutorials', 'Career Advice']
  },
  target_audience: {
    label: 'Target Audience',
    type: 'multiselect',
    options: ['Developers', 'Researchers', 'Business Leaders', 'Students', 'General Public']
  },
  subscriber_count_min: {
    label: 'Min Subscribers',
    type: 'number',
    placeholder: '1000'
  },
  subscriber_count_max: {
    label: 'Max Subscribers',
    type: 'number',
    placeholder: '100000'
  },
  is_free: {
    label: 'Free Newsletter',
    type: 'boolean'
  },
  has_archives: {
    label: 'Has Archives',
    type: 'boolean'
  },
  author: {
    label: 'Author',
    type: 'text',
    placeholder: 'e.g., Andrew Ng, Benedict Evans...'
  },
  focus_areas_search: {
    label: 'Focus Areas Search',
    type: 'text',
    placeholder: 'e.g., machine learning, AI news...'
  },
  audience_search: {
    label: 'Audience Search',
    type: 'text',
    placeholder: 'e.g., developers, business...'
  },
  frequency_search: {
    label: 'Frequency Search',
    type: 'text',
    placeholder: 'e.g., weekly, daily...'
  }
};

const BOOK_FILTERS = {
  author: {
    label: 'Author',
    type: 'text',
    placeholder: 'e.g., Stuart Russell, Pedro Domingos...'
  },
  publisher: {
    label: 'Publisher',
    type: 'text',
    placeholder: 'e.g., MIT Press, O\'Reilly...'
  },
  isbn: {
    label: 'ISBN',
    type: 'text',
    placeholder: 'e.g., 978-0262039...'
  },
  formats: {
    label: 'Formats',
    type: 'multiselect',
    options: ['Hardcover', 'Paperback', 'eBook', 'Audiobook', 'PDF']
  },
  publication_date_from: {
    label: 'Publication Date From',
    type: 'date'
  },
  publication_date_to: {
    label: 'Publication Date To',
    type: 'date'
  },
  page_count_min: {
    label: 'Min Pages',
    type: 'number',
    placeholder: '200'
  },
  page_count_max: {
    label: 'Max Pages',
    type: 'number',
    placeholder: '1000'
  },
  has_purchase_url: {
    label: 'Purchase Link Available',
    type: 'boolean'
  },
  summary_search: {
    label: 'Summary Search',
    type: 'text',
    placeholder: 'e.g., artificial intelligence, machine learning...'
  },
  format_search: {
    label: 'Format Search',
    type: 'text',
    placeholder: 'e.g., ebook, audiobook...'
  }
};

const ENTITY_TYPE_CONFIGS = {
  // Current backend entity types
  'AI Tool': { filters: TOOL_FILTERS, icon: Wrench, color: 'text-indigo-600' },
  'API': { filters: SOFTWARE_FILTERS, icon: Code, color: 'text-emerald-600' },
  'Dataset': { filters: SOFTWARE_FILTERS, icon: FileText, color: 'text-red-600' },
  'Library': { filters: SOFTWARE_FILTERS, icon: Code, color: 'text-emerald-600' },
  'Model': { filters: SOFTWARE_FILTERS, icon: Cpu, color: 'text-purple-600' },
  'Platform': { filters: TOOL_FILTERS, icon: Wrench, color: 'text-indigo-600' },
  'Service': { filters: TOOL_FILTERS, icon: Wrench, color: 'text-indigo-600' },

  // Future entity types (from API spec)
  'Course': { filters: COURSE_FILTERS, icon: GraduationCap, color: 'text-blue-600' },
  'Job': { filters: JOB_FILTERS, icon: Briefcase, color: 'text-green-600' },
  'Hardware': { filters: HARDWARE_FILTERS, icon: Cpu, color: 'text-purple-600' },
  'Event': { filters: EVENT_FILTERS, icon: Calendar, color: 'text-orange-600' },
  'Tool': { filters: TOOL_FILTERS, icon: Wrench, color: 'text-indigo-600' },
  'Agency': { filters: AGENCY_FILTERS, icon: Building2, color: 'text-cyan-600' },
  'Software': { filters: SOFTWARE_FILTERS, icon: Code, color: 'text-emerald-600' },
  'Research Paper': { filters: RESEARCH_PAPER_FILTERS, icon: FileText, color: 'text-red-600' },
  'Podcast': { filters: PODCAST_FILTERS, icon: Headphones, color: 'text-pink-600' },
  'Community': { filters: COMMUNITY_FILTERS, icon: Users, color: 'text-violet-600' },
  'Grant': { filters: GRANT_FILTERS, icon: DollarSign, color: 'text-yellow-600' },
  'Newsletter': { filters: NEWSLETTER_FILTERS, icon: Mail, color: 'text-teal-600' },
  'Book': { filters: BOOK_FILTERS, icon: BookOpen, color: 'text-amber-600' },
};

const EntitySpecificFilters: React.FC<EntitySpecificFiltersProps> = ({
  selectedEntityTypes,
  onFilterChange,
  allEntityTypes,
  currentFilters = {},
}) => {
  // Get the relevant entity types that have specific filters
  const relevantEntityTypes = selectedEntityTypes
    .map(id => allEntityTypes.find(type => type.id === id))
    .filter(type => type && ENTITY_TYPE_CONFIGS[type.name])
    .map(type => type!);



  if (relevantEntityTypes.length === 0) {
    return null;
  }

  const handleFilterChange = (entityType: string, filterKey: string, value: any) => {
    // Clean up empty values
    const cleanValue = (value === null || value === undefined || value === '' ||
        (Array.isArray(value) && value.length === 0)) ? null : value;

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [EntitySpecificFilters] Filter change:', { entityType, filterKey, value: cleanValue });
    }

    // Call onFilterChange with the filter key and value
    onFilterChange(filterKey, cleanValue);
  };

  const handleMultiSelectToggle = (entityType: string, filterKey: string, value: string, currentValues: string[] = []) => {
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    
    handleFilterChange(entityType, filterKey, newValues.length > 0 ? newValues : null);
  };

  const renderFilter = (entityType: string, filterKey: string, filterConfig: any) => {
    // Get current value from props (flat parameter structure)
    const currentValue = currentFilters[filterKey];

    switch (filterConfig.type) {
      case 'boolean':
        return (
          <div className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
            currentValue
              ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-600'
              : 'bg-gray-50 border-gray-200 hover:bg-blue-50 hover:border-blue-200 dark:bg-gray-700/50 dark:border-gray-600 dark:hover:bg-blue-900/20 dark:hover:border-blue-600'
          }`}
          onClick={() => handleFilterChange(entityType, filterKey, !currentValue)}
          data-testid={`${filterKey.replace(/_/g, '-')}-filter`}>
            <Checkbox
              id={`${entityType}-${filterKey}`}
              checked={currentValue || false}
              onCheckedChange={(checked) => handleFilterChange(entityType, filterKey, checked || null)}
              className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
            />
            <Label htmlFor={`${entityType}-${filterKey}`} className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
              {filterConfig.label}
            </Label>
          </div>
        );

      case 'text':
        return (
          <div className="space-y-2" data-testid={`${filterKey.replace(/_/g, '-')}-filter`}>
            <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">{filterConfig.label}</Label>
            <input
              type="text"
              placeholder={filterConfig.placeholder}
              value={currentValue || ''}
              onChange={(e) => handleFilterChange(entityType, filterKey, e.target.value || null)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        );

      case 'number':
        return (
          <div className="space-y-2" data-testid={`${filterKey.replace(/_/g, '-')}-filter`}>
            <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">{filterConfig.label}</Label>
            <input
              type="number"
              placeholder={filterConfig.placeholder}
              value={currentValue || ''}
              onChange={(e) => handleFilterChange(entityType, filterKey, e.target.value ? parseInt(e.target.value) : null)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        );

      case 'date':
        return (
          <div className="space-y-2" data-testid={`${filterKey.replace(/_/g, '-')}-filter`}>
            <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">{filterConfig.label}</Label>
            <input
              type="date"
              value={currentValue || ''}
              onChange={(e) => handleFilterChange(entityType, filterKey, e.target.value || null)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        );

      case 'multiselect':
        return (
          <div className="space-y-3" data-testid={`${filterKey.replace(/_/g, '-')}-filter`}>
            <Label className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {filterConfig.label}
            </Label>
            <div className="grid grid-cols-2 gap-2">
              {filterConfig.options.map((option: string) => {
                const isSelected = (currentValue || []).includes(option);
                return (
                  <div key={option} className={`flex items-center space-x-3 p-2 rounded-lg border transition-all duration-200 cursor-pointer ${
                    isSelected
                      ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-600'
                      : 'bg-gray-50 border-gray-200 hover:bg-blue-50 hover:border-blue-200 dark:bg-gray-700/50 dark:border-gray-600 dark:hover:bg-blue-900/20 dark:hover:border-blue-600'
                  }`} onClick={() => handleMultiSelectToggle(entityType, filterKey, option, currentValue || [])}>
                    <Checkbox
                      id={`${entityType}-${filterKey}-${option}`}
                      checked={isSelected}
                      onCheckedChange={() => handleMultiSelectToggle(entityType, filterKey, option, currentValue || [])}
                      className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <Label htmlFor={`${entityType}-${filterKey}-${option}`} className="text-xs font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
                      {option}
                    </Label>
                  </div>
                );
              })}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6" data-testid="entity-specific-filters">

      {/* ✅ FUNCTIONAL FILTERS */}
      {relevantEntityTypes.map(entityType => {
        const config = ENTITY_TYPE_CONFIGS[entityType.name];
        const Icon = config.icon;

        return (
          <div key={entityType.id} className="space-y-6" data-testid={`${entityType.name.toLowerCase().replace(' ', '-')}-filters`}>
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20`}>
                <Icon className={`h-5 w-5 ${config.color}`} />
              </div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {entityType.name} Filters
              </h4>
            </div>

            <div className="space-y-6">
              {Object.entries(config.filters).map(([filterKey, filterConfig]) => (
                <div key={filterKey}>
                  {renderFilter(entityType.name, filterKey, filterConfig)}
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default EntitySpecificFilters;
